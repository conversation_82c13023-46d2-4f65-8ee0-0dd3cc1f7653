import React, { useState, useEffect } from "react";
import {
  BrowserRouter as Router,
  Routes,
  Route,
  Link,
  Navigate,
} from "react-router-dom";

import Home from "./Home.jsx";
import Uploads from "./upload.jsx";
import AuthForm from "./AuthForm.jsx";
import AdminPromptPage from "./AdminPromptPage.jsx";
import AdminDashboard from "./AdminDashboard.jsx";
import EscalatedTicketsPage from "./EscalatedTicketsPage.jsx";
import AdminChatbot from "./AdminChatbot.jsx";
import ActionsPage from "./ActionsPage.jsx";
import NewTicketForm from "./NewTicketForm.jsx";
import BrandSelectionPage from "./BrandSelectionPage.jsx";
import HierarchicalProductSelection from "./HierarchicalProductSelection.jsx";
import NewTicketDetailsForm from "./NewTicketDetailsForm.jsx";
import SelectTicketPage from "./SelectTicketPage.jsx";
import StructuredChatbot from "./StructuredChatbot.jsx";
import ProblemCategoriesPage from "./ProblemCategoriesPage.jsx";
import ProblemDescriptionPage from "./ProblemDescriptionPage.jsx";
import PendingTicketsList from "./PendingTicketsList.jsx";
import UsageDashboard from "./UsageDashboard.jsx";

function App() {
  const [user, setUser] = useState(null);

  useEffect(() => {
    // Check for existing authentication data on app startup
    const userData = localStorage.getItem("userData");
    const accessToken = localStorage.getItem("access");

    if (userData && accessToken) {
      try {
        const parsedUser = JSON.parse(userData);
        setUser(parsedUser);
        console.log("User authenticated from localStorage:", parsedUser.email);
      } catch (error) {
        console.error("Error parsing user data:", error);
        // Clear corrupted data
        localStorage.removeItem("userData");
        localStorage.removeItem("access");
        localStorage.removeItem("refresh");
        setUser(null);
      }
    } else {
      console.log("No authentication data found. User will need to login.");
      setUser(null);
    }
  }, []);

  const handleLoginSuccess = (userData, tokens) => {
    setUser(userData);
    localStorage.setItem("userData", JSON.stringify(userData));
    localStorage.setItem("access", tokens.access);
    localStorage.setItem("refresh", tokens.refresh);
  };

  const handleLogout = () => {
    setUser(null);
    localStorage.removeItem("userData");
    localStorage.removeItem("access");
    localStorage.removeItem("refresh");
  };

  const PrivateRoute = ({ children }) => (user ? children : <Navigate to="/auth" replace />);
  const AdminRoute = ({ children }) =>
    user && user.is_admin ? children : <Navigate to="/" replace />;

  return (
    <Router>
      {window.location.pathname !== "/admin" && (
        <nav style={{ padding: "10px", borderBottom: "1px solid #ccc", display: "flex", justifyContent: "space-between" }}>
          <div>
            {user && (
              <>
                <Link to={user.is_admin ? "/admin" : "/"} style={{ marginRight: 15 }}>
                  {user.is_admin ? "Admin Panel" : "Chatbot"}
                </Link>
                <Link to="/uploads" style={{ marginRight: 15 }}>
                  Uploads
                </Link>
                {user.is_admin && (
                  <Link to="/prompt-manager" style={{ marginRight: 15 }}>
                    Prompt Admin
                  </Link>
                )}
              </>
            )}
          </div>

          <div>
            {user ? (
              <button
                onClick={handleLogout}
                style={{
                  cursor: "pointer",
                  backgroundColor: "#f44336",
                  color: "white",
                  border: "none",
                  padding: "6px 12px",
                  borderRadius: "4px",
                }}
              >
                Logout
              </button>
            ) : (
              <>
                <Link to="/auth" style={{ marginRight: 12 }}>
                  Login
                </Link>
                <Link to="/signup">Sign Up
                </Link>
              </>
            )}
          </div>
        </nav>
      )}

      <Routes>
        <Route
          path="/"
          element={
            user ? (user.is_admin ? <Navigate to="/admin" replace /> : <Navigate to="/actions" replace />) : <Navigate to="/auth" replace />
          }
        />

        <Route
          path="/actions"
          element={
            <PrivateRoute>
              <ActionsPage token={localStorage.getItem("access")} />
            </PrivateRoute>
          }
        />

        <Route
          path="/new-ticket"
          element={
            <PrivateRoute>
              <NewTicketForm token={localStorage.getItem("access")} />
            </PrivateRoute>
          }
        />

        <Route
          path="/select-brand"
          element={
            <PrivateRoute>
              <BrandSelectionPage token={localStorage.getItem("access")} />
            </PrivateRoute>
          }
        />

        <Route
          path="/hierarchical-selection"
          element={
            <PrivateRoute>
              <HierarchicalProductSelection token={localStorage.getItem("access")} />
            </PrivateRoute>
          }
        />

        <Route
          path="/new-ticket-details"
          element={
            <PrivateRoute>
              <NewTicketDetailsForm token={localStorage.getItem("access")} />
            </PrivateRoute>
          }
        />

        <Route
          path="/pending-tickets"
          element={
            <PrivateRoute>
              <PendingTicketsList />
            </PrivateRoute>
          }
        />

        <Route
          path="/select-ticket"
          element={
            <PrivateRoute>
              <SelectTicketPage token={localStorage.getItem("access")} />
            </PrivateRoute>
          }
        />

        <Route
          path="/problem-categories"
          element={
            <PrivateRoute>
              <ProblemCategoriesPage token={localStorage.getItem("access")} />
            </PrivateRoute>
          }
        />

        <Route
          path="/problem-description"
          element={
            <PrivateRoute>
              <ProblemDescriptionPage token={localStorage.getItem("access")} />
            </PrivateRoute>
          }
        />

        <Route
          path="/chatbot/:ticketId"
          element={
            <PrivateRoute>
              <Home token={localStorage.getItem("access")} />
            </PrivateRoute>
          }
        />

        <Route
          path="/legacy-chat"
          element={
            <PrivateRoute>
              <Home token={localStorage.getItem("access")} />
            </PrivateRoute>
          }
        />

        <Route
          path="/structured-chat/:ticketId"
          element={
            <PrivateRoute>
              <StructuredChatbot token={localStorage.getItem("access")} />
            </PrivateRoute>
          }
        />

        <Route
          path="/uploads"
          element={
            <PrivateRoute>
              <Uploads />
            </PrivateRoute>
          }
        />

        <Route
          path="/prompt-manager"
          element={
            <AdminRoute>
              <AdminPromptPage />
            </AdminRoute>
          }
        />

        <Route
          path="/admin"
          element={
            <AdminRoute>
              <AdminDashboard />
            </AdminRoute>
          }
        />

        <Route
          path="/admin/tickets"
          element={
            <AdminRoute>
              <EscalatedTicketsPage />
            </AdminRoute>
          }
        />

        <Route
          path="/auth"
          element={<AuthForm onLoginSuccess={handleLoginSuccess} />}
        />
        <Route
          path="/admin/chatbot"
          element={
            <AdminRoute>
              <AdminChatbot />
            </AdminRoute>
          }
        />

        <Route
          path="/usage"
          element={
            <AdminRoute>
              <UsageDashboard />
            </AdminRoute>
          }
        />

        <Route
          path="/signup"
          element={<AuthForm defaultMode="signup" onLoginSuccess={handleLoginSuccess} />}
        />
      </Routes>
    </Router>
  );
}

export default App;