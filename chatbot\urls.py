from django.urls import path
from rest_framework_simplejwt.views import TokenRefreshView
from chatbot import views
from chatbot.views import EmailLoginView
from .views import PromptTemplateView, upload_pdf_view
from .views import verify_organization
from chatbot.views import user_info
from .views import update_ticket_status
from .views import pending_ticket_summaries
from .views import (
    escalated_tickets,
    escalated_ticket_detail,
    run_processing_pipeline,
)
from .usage_views import (
    usage_summary,
    usage_logs,
    export_usage_csv,
)

urlpatterns = [
    # Authentication
    path('api/signup/', views.signup_view, name='signup'),
    path('api/token/', EmailLoginView.as_view(), name='token_obtain_pair'),
    path('api/token/refresh/', TokenRefreshView.as_view(), name='token_refresh'),

    # Chat & File APIs
    path('api/chat/', views.chat, name='chat'),
    path('api/upload_pdf/', upload_pdf_view, name='upload_pdf'),  # single entry only

    path('api/file/<str:filename>/', views.serve_file_view, name='serve_file'),
    path('api/files/<str:filename>/', views.serve_file_view, name='serve_file_alias'),

    # Health check and home page
    path('api/health/', views.health, name='health'),
    path('', views.home, name='home'),

    path('api/prompts/', PromptTemplateView.as_view(), name='prompt-template'),

    path('api/verify_organization/', verify_organization, name='verify_organization'),
    path('api/user_info/', user_info, name='user_info'),
    path('api/product_hierarchy/', views.get_product_hierarchy, name='get_product_hierarchy'),
    path('api/create_ticket/', views.create_ticket, name='create_ticket'),
    path('api/add_problem_description/', views.add_problem_description, name='add_problem_description'),
    path('api/update_ticket_categories/', views.update_ticket_categories, name='update_ticket_categories'),
    path('api/update_ticket_description/', views.update_ticket_description, name='update_ticket_description'),
    path('api/update_ticket_status/', update_ticket_status, name='update_ticket_status'),
    path('api/view_ticket_prompt/<str:ticket_number>/', views.view_ticket_prompt, name='view_ticket_prompt'),
    # pending-ticket APIs
    path('api/open_tickets/',   views.open_tickets,   name='open_tickets'),
    path('api/ticket/<str:ticket_number>/', views.ticket_detail, name='ticket_detail'),
    path("api/pending_tickets/", views.pending_tickets, name="pending_tickets"),
    path("api/closed_tickets/", views.closed_tickets, name="closed_tickets"),
    path("api/ticket_summary/", views.ticket_summary, name="ticket_summary"),
    path('api/pending_ticket_summaries/', pending_ticket_summaries, name='pending_ticket_summaries'),

    # New session management endpoints
    path('api/start_ticket_session/<str:ticket_number>/', views.start_ticket_session, name='start_ticket_session'),
    path('api/start_general_session/', views.start_general_session, name='start_general_session'),
    path('api/end_session/', views.end_session, name='end_session'),

    path("escalated/", escalated_tickets),
    path("escalated/<str:ticket_number>/", escalated_ticket_detail),
    path("api/tickets/process_uploads/", run_processing_pipeline, name="run_processing_pipeline"),

    # Usage Dashboard API endpoints
    path('api/usage/summary/', usage_summary, name='usage_summary'),
    path('api/usage/logs/', usage_logs, name='usage_logs'),
    path('api/usage/export/', export_usage_csv, name='export_usage_csv'),

]
