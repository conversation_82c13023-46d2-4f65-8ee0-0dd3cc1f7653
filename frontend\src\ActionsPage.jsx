import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import "./App.css";

const BACKEND_URL = "http://localhost:8000";

export default function ActionsPage({ token }) {
  const navigate = useNavigate();
  const accessToken = token || localStorage.getItem("access");
  const [userName, setUserName] = useState("");
  const [showClosedTickets, setShowClosedTickets] = useState(false);
  const [closedTickets, setClosedTickets] = useState([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (!accessToken) {
      navigate("/auth");
      return;
    }

    // Fetch user info
    fetch(`${BACKEND_URL}/api/user_info/`, {
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${accessToken}`,
      },
    })
      .then(async (res) => {
        if (!res.ok) {
          throw new Error("User info fetch failed");
        }
        return res.json();
      })
      .then((data) => {
        setUserName(data.name || data.username || data.email);
      })
      .catch((err) => {
        console.error("Failed to fetch user info:", err);
        localStorage.removeItem("access");
        navigate("/auth");
      });
  }, [accessToken, navigate]);

  const handleRaiseNewTicket = () => {
    navigate("/select-brand");
  };

  const handleUsePendingTicket = () => {
    // Navigate to pending tickets list page
    navigate("/pending-tickets");
  };

  const handleViewClosedTickets = async () => {
    setLoading(true);
    try {
      const response = await fetch(`${BACKEND_URL}/api/closed_tickets/`, {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      });
      const data = await response.json();

      if (response.ok) {
        setClosedTickets(data.tickets || []);
        setShowClosedTickets(true);
      } else {
        console.error("Failed to fetch closed tickets:", data);
        alert("Failed to fetch closed tickets. Please try again.");
      }
    } catch (err) {
      console.error("Error fetching closed tickets:", err);
      alert("Network error. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={{ 
      padding: "40px", 
      maxWidth: "800px", 
      margin: "0 auto", 
      textAlign: "center",
      fontFamily: "Arial, sans-serif"
    }}>
      <h1 style={{ 
        color: "#333", 
        marginBottom: "20px",
        fontSize: "2.5rem"
      }}>
        Welcome, {userName}!
      </h1>
      
      <p style={{ 
        fontSize: "1.2rem", 
        color: "#666", 
        marginBottom: "40px",
        lineHeight: "1.6"
      }}>
        How can we help you today? Please choose one of the options below:
      </p>

      <div style={{ 
        display: "flex", 
        flexDirection: "column", 
        gap: "20px", 
        alignItems: "center" 
      }}>
        
        {/* Raise New Ticket Button */}
        <button
          onClick={handleRaiseNewTicket}
          style={{
            backgroundColor: "#4CAF50",
            color: "white",
            border: "none",
            padding: "20px 40px",
            fontSize: "1.3rem",
            borderRadius: "8px",
            cursor: "pointer",
            width: "300px",
            boxShadow: "0 4px 8px rgba(0,0,0,0.1)",
            transition: "all 0.3s ease",
          }}
          onMouseOver={(e) => {
            e.target.style.backgroundColor = "#45a049";
            e.target.style.transform = "translateY(-2px)";
          }}
          onMouseOut={(e) => {
            e.target.style.backgroundColor = "#4CAF50";
            e.target.style.transform = "translateY(0)";
          }}
        >
          🎫 Raise New Ticket
        </button>

        {/* Use Pending Ticket Button */}
        <button
          onClick={handleUsePendingTicket}
          style={{
            backgroundColor: "#2196F3",
            color: "white",
            border: "none",
            padding: "20px 40px",
            fontSize: "1.3rem",
            borderRadius: "8px",
            cursor: "pointer",
            width: "300px",
            boxShadow: "0 4px 8px rgba(0,0,0,0.1)",
            transition: "all 0.3s ease",
          }}
          onMouseOver={(e) => {
            e.target.style.backgroundColor = "#1976D2";
            e.target.style.transform = "translateY(-2px)";
          }}
          onMouseOut={(e) => {
            e.target.style.backgroundColor = "#2196F3";
            e.target.style.transform = "translateY(0)";
          }}
        >
          📋 Use Pending Ticket
        </button>

        {/* Closed Tickets Button */}
        <button
          onClick={handleViewClosedTickets}
          disabled={loading}
          style={{
            backgroundColor: loading ? "#ccc" : "#FF9800",
            color: "white",
            border: "none",
            padding: "20px 40px",
            fontSize: "1.3rem",
            borderRadius: "8px",
            cursor: loading ? "not-allowed" : "pointer",
            width: "300px",
            boxShadow: "0 4px 8px rgba(0,0,0,0.1)",
            transition: "all 0.3s ease",
          }}
          onMouseOver={(e) => {
            if (!loading) {
              e.target.style.backgroundColor = "#F57C00";
              e.target.style.transform = "translateY(-2px)";
            }
          }}
          onMouseOut={(e) => {
            if (!loading) {
              e.target.style.backgroundColor = "#FF9800";
              e.target.style.transform = "translateY(0)";
            }
          }}
        >
          {loading ? "Loading..." : "📁 Closed Tickets"}
        </button>
      </div>

      {/* Closed Tickets Modal */}
      {showClosedTickets && (
        <div style={{
          position: "fixed",
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: "rgba(0,0,0,0.5)",
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          zIndex: 1000
        }}>
          <div style={{
            backgroundColor: "white",
            borderRadius: "8px",
            padding: "30px",
            maxWidth: "800px",
            maxHeight: "80vh",
            overflowY: "auto",
            width: "90%"
          }}>
            <div style={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              marginBottom: "20px"
            }}>
              <h2 style={{ margin: 0, color: "#333" }}>Closed Tickets</h2>
              <button
                onClick={() => setShowClosedTickets(false)}
                style={{
                  backgroundColor: "#f44336",
                  color: "white",
                  border: "none",
                  borderRadius: "4px",
                  padding: "8px 16px",
                  cursor: "pointer"
                }}
              >
                ✕ Close
              </button>
            </div>

            {closedTickets.length === 0 ? (
              <p style={{ textAlign: "center", color: "#666", fontSize: "1.1rem" }}>
                No closed tickets found.
              </p>
            ) : (
              <div style={{ display: "flex", flexDirection: "column", gap: "15px" }}>
                {closedTickets.map((ticket) => (
                  <div
                    key={ticket.ticket_number}
                    style={{
                      border: "1px solid #ddd",
                      borderRadius: "6px",
                      padding: "15px",
                      backgroundColor: "#f9f9f9"
                    }}
                  >
                    <div style={{
                      display: "flex",
                      justifyContent: "space-between",
                      alignItems: "flex-start",
                      marginBottom: "10px"
                    }}>
                      <h3 style={{ margin: 0, color: "#333", fontSize: "1.1rem" }}>
                        {ticket.ticket_number}
                      </h3>
                      <span style={{
                        backgroundColor: "#4CAF50",
                        color: "white",
                        padding: "4px 8px",
                        borderRadius: "4px",
                        fontSize: "0.8rem",
                        fontWeight: "bold"
                      }}>
                        CLOSED
                      </span>
                    </div>
                    <p style={{ margin: "5px 0", color: "#666" }}>
                      <strong>Product:</strong> {ticket.product_type} - {ticket.model}
                    </p>
                    <p style={{ margin: "5px 0", color: "#666" }}>
                      <strong>Issue:</strong> {ticket.issue}
                    </p>
                    <p style={{ margin: "5px 0", color: "#666" }}>
                      <strong>Solution:</strong> {ticket.solution_summary || "No solution summary available"}
                    </p>
                    <p style={{ margin: "5px 0", color: "#999", fontSize: "0.9rem" }}>
                      <strong>Closed:</strong> {new Date(ticket.last_activity).toLocaleDateString()}
                    </p>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      )}

    </div>
  );
}
