import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import "./App.css";

const BACKEND_URL = "http://localhost:8000";

export default function ActionsPage({ token }) {
  const navigate = useNavigate();
  const accessToken = token || localStorage.getItem("access");
  const [userName, setUserName] = useState("");

  useEffect(() => {
    if (!accessToken) {
      navigate("/auth");
      return;
    }

    // Fetch user info
    fetch(`${BACKEND_URL}/api/user_info/`, {
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${accessToken}`,
      },
    })
      .then(async (res) => {
        if (!res.ok) {
          throw new Error("User info fetch failed");
        }
        return res.json();
      })
      .then((data) => {
        setUserName(data.name || data.username || data.email);
      })
      .catch((err) => {
        console.error("Failed to fetch user info:", err);
        localStorage.removeItem("access");
        navigate("/auth");
      });
  }, [accessToken, navigate]);

  const handleRaiseNewTicket = () => {
    navigate("/hierarchical-selection");
  };

  const handleUsePendingTicket = () => {
    // Navigate to pending tickets list page
    navigate("/pending-tickets");
  };

  return (
    <div style={{ 
      padding: "40px", 
      maxWidth: "800px", 
      margin: "0 auto", 
      textAlign: "center",
      fontFamily: "Arial, sans-serif"
    }}>
      <h1 style={{ 
        color: "#333", 
        marginBottom: "20px",
        fontSize: "2.5rem"
      }}>
        Welcome, {userName}!
      </h1>
      
      <p style={{ 
        fontSize: "1.2rem", 
        color: "#666", 
        marginBottom: "40px",
        lineHeight: "1.6"
      }}>
        How can we help you today? Please choose one of the options below:
      </p>

      <div style={{ 
        display: "flex", 
        flexDirection: "column", 
        gap: "20px", 
        alignItems: "center" 
      }}>
        
        {/* Raise New Ticket Button */}
        <button
          onClick={handleRaiseNewTicket}
          style={{
            backgroundColor: "#4CAF50",
            color: "white",
            border: "none",
            padding: "20px 40px",
            fontSize: "1.3rem",
            borderRadius: "8px",
            cursor: "pointer",
            width: "300px",
            boxShadow: "0 4px 8px rgba(0,0,0,0.1)",
            transition: "all 0.3s ease",
          }}
          onMouseOver={(e) => {
            e.target.style.backgroundColor = "#45a049";
            e.target.style.transform = "translateY(-2px)";
          }}
          onMouseOut={(e) => {
            e.target.style.backgroundColor = "#4CAF50";
            e.target.style.transform = "translateY(0)";
          }}
        >
          🎫 Raise New Ticket
        </button>

        {/* Use Pending Ticket Button */}
        <button
          onClick={handleUsePendingTicket}
          style={{
            backgroundColor: "#2196F3",
            color: "white",
            border: "none",
            padding: "20px 40px",
            fontSize: "1.3rem",
            borderRadius: "8px",
            cursor: "pointer",
            width: "300px",
            boxShadow: "0 4px 8px rgba(0,0,0,0.1)",
            transition: "all 0.3s ease",
          }}
          onMouseOver={(e) => {
            e.target.style.backgroundColor = "#1976D2";
            e.target.style.transform = "translateY(-2px)";
          }}
          onMouseOut={(e) => {
            e.target.style.backgroundColor = "#2196F3";
            e.target.style.transform = "translateY(0)";
          }}
        >
          📋 Use Pending Ticket
        </button>
      </div>


    </div>
  );
}
