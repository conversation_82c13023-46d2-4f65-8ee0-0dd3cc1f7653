import React from "react";
import { useNavigate } from "react-router-dom";

export default function BrandSelectionPage({ token }) {
  const navigate = useNavigate();

  const brands = ["DALSA", "FLIR", "Basler", "Allied Vision", "Cognex", "Other"];

  const handleBrandClick = (brand) => {
    if (brand === "DALSA") {
      // Store selected brand in sessionStorage for later use
      sessionStorage.setItem('selectedBrand', brand);
      navigate("/hierarchical-selection");
    } else {
      // Placeholder for other brands - currently do nothing
      console.log(`${brand} clicked - functionality to be implemented`);
    }
  };

  return (
    <div style={{ 
      padding: "40px", 
      maxWidth: "800px", 
      margin: "0 auto", 
      textAlign: "center",
      fontFamily: "Arial, sans-serif"
    }}>
      <h1 style={{ 
        color: "#333", 
        marginBottom: "20px",
        fontSize: "2.5rem"
      }}>
        Select Your Brand
      </h1>
      
      <p style={{ 
        fontSize: "1.2rem", 
        color: "#666", 
        marginBottom: "40px",
        lineHeight: "1.6"
      }}>
        Please select your camera brand to continue:
      </p>

      <div style={{ 
        display: "grid", 
        gridTemplateColumns: "repeat(auto-fit, minmax(250px, 1fr))",
        gap: "20px", 
        alignItems: "center",
        maxWidth: "600px",
        margin: "0 auto"
      }}>
        {brands.map((brand) => (
          <button
            key={brand}
            onClick={() => handleBrandClick(brand)}
            style={{
              backgroundColor: brand === "DALSA" ? "#4CAF50" : "#2196F3",
              color: "white",
              border: "none",
              padding: "20px 30px",
              fontSize: "1.2rem",
              borderRadius: "8px",
              cursor: "pointer",
              boxShadow: "0 4px 8px rgba(0,0,0,0.1)",
              transition: "all 0.3s ease",
              fontWeight: "bold"
            }}
            onMouseOver={(e) => {
              e.target.style.backgroundColor = brand === "DALSA" ? "#45a049" : "#1976D2";
              e.target.style.transform = "translateY(-2px)";
            }}
            onMouseOut={(e) => {
              e.target.style.backgroundColor = brand === "DALSA" ? "#4CAF50" : "#2196F3";
              e.target.style.transform = "translateY(0)";
            }}
          >
            {brand}
          </button>
        ))}
      </div>

      <div style={{ marginTop: "40px" }}>
        <button
          onClick={() => navigate("/actions")}
          style={{
            padding: "15px 30px",
            backgroundColor: "#757575",
            color: "white",
            border: "none",
            borderRadius: "6px",
            fontSize: "16px",
            cursor: "pointer",
            transition: "background-color 0.2s ease"
          }}
          onMouseOver={(e) => {
            e.target.style.backgroundColor = "#616161";
          }}
          onMouseOut={(e) => {
            e.target.style.backgroundColor = "#757575";
          }}
        >
          ← Back to Actions
        </button>
      </div>
    </div>
  );
}
