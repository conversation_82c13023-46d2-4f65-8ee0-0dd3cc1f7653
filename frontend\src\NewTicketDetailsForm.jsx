import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { BACKEND_URL } from "./utils/api";

export default function NewTicketDetailsForm({ token }) {
  const navigate = useNavigate();
  const accessToken = token || localStorage.getItem("access");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [hierarchicalData, setHierarchicalData] = useState(null);

  const [formData, setFormData] = useState({
    sensorType: "",
    familyName: "",
    modelNumber: "",
    serialNumber: "",
    sdkSoftwareUsed: "",
    sdkVersion: "",
    programmingLanguage: "",
    cameraConfigurationTool: "",
    operatingSystemDetailed: "",
    purchasedFrom: "",
    yearOfPurchase: "",
    poNumber: "",
    software: "",
  });

  // Load hierarchical selection data
  useEffect(() => {
    const storedData = sessionStorage.getItem('hierarchicalSelection');
    if (!storedData) {
      // No hierarchical selection found, redirect back
      navigate('/hierarchical-selection');
      return;
    }

    try {
      const data = JSON.parse(storedData);
      setHierarchicalData(data);
      
      // Pre-populate form fields based on hierarchical selection
      setFormData(prev => ({
        ...prev,
        // Set sensor type if it's a camera
        sensorType: data.productSubcategory === "Area Scan" || data.productSubcategory === "Line Scan" 
          ? data.productSubcategory : "",
        // Set family name from hierarchy
        familyName: data.productFamily || "",
        // Set SDK if it's software
        sdkSoftwareUsed: data.productCategory === "Software" ? data.productSubcategory : "",
      }));
    } catch (err) {
      console.error("Error parsing hierarchical data:", err);
      navigate('/hierarchical-selection');
    }
  }, [navigate]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    if (error) setError("");
  };

  const validateForm = () => {
    const requiredFields = [
      'modelNumber', 'serialNumber', 'purchasedFrom', 'yearOfPurchase', 'operatingSystemDetailed', 'poNumber'
    ];

    for (let field of requiredFields) {
      if (!formData[field] || formData[field].trim() === "") {
        return `Please fill in the ${field.replace(/([A-Z])/g, ' $1').toLowerCase()} field.`;
      }
    }

    return null;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    const validationError = validateForm();
    if (validationError) {
      setError(validationError);
      return;
    }

    if (!hierarchicalData) {
      setError("Hierarchical selection data is missing. Please start over.");
      return;
    }

    setLoading(true);
    setError("");

    try {
      // Get brand from sessionStorage
      const selectedBrand = sessionStorage.getItem('selectedBrand') || '';

      const response = await fetch(`${BACKEND_URL}/api/create_ticket/`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${accessToken}`,
        },
        body: JSON.stringify({
          // Hierarchical data
          product_type: hierarchicalData.productCategory,
          product_hierarchy_path: hierarchicalData.path,
          product_category: hierarchicalData.productCategory,
          product_subcategory: hierarchicalData.productSubcategory,
          product_family: hierarchicalData.productFamily,
          product_interface: hierarchicalData.productInterface,

          // Form data
          brand: selectedBrand,
          sensor_type: formData.sensorType,
          family_name: formData.familyName,
          model_number: formData.modelNumber,
          serial_number: formData.serialNumber,
          sdk_software_used: formData.sdkSoftwareUsed,
          sdk_version: formData.sdkVersion,
          programming_language: formData.programmingLanguage,
          camera_configuration_tool: formData.cameraConfigurationTool,
          operating_system_detailed: formData.operatingSystemDetailed,
          purchased_from: formData.purchasedFrom,
          year_of_purchase: formData.yearOfPurchase,
          po_number: formData.poNumber,
          software: formData.software,
        }),
      });

      const data = await response.json();

      if (response.ok) {
        // Clear the hierarchical selection and selected brand from session storage
        sessionStorage.removeItem('hierarchicalSelection');
        sessionStorage.removeItem('selectedBrand');
        // Redirect to problem category selection
        navigate(`/problem-categories?ticket=${data.ticket_number}`);
      } else {
        setError(data.message || "Failed to create ticket. Please try again.");
      }
    } catch (err) {
      console.error("Error creating ticket:", err);
      setError("Network error. Please check your connection and try again.");
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    sessionStorage.removeItem('hierarchicalSelection');
    sessionStorage.removeItem('selectedBrand');
    navigate("/select-brand");
  };

  if (!hierarchicalData) {
    return (
      <div style={{ 
        padding: "40px", 
        textAlign: "center",
        fontFamily: "Arial, sans-serif"
      }}>
        <h2>Loading...</h2>
      </div>
    );
  }

  return (
    <div style={{ 
      padding: "40px", 
      maxWidth: "800px", 
      margin: "0 auto", 
      fontFamily: "Arial, sans-serif"
    }}>
      <h1 style={{ 
        color: "#333", 
        marginBottom: "20px",
        textAlign: "center"
      }}>
        Product Details
      </h1>

      {/* Show selected hierarchy */}
      <div style={{
        backgroundColor: "#e8f5e8",
        padding: "15px",
        borderRadius: "8px",
        marginBottom: "30px",
        border: "1px solid #4CAF50"
      }}>
        <h3 style={{ margin: "0 0 10px 0", color: "#2e7d32" }}>Selected Product:</h3>
        <p style={{ margin: "0", fontSize: "16px", fontWeight: "bold" }}>
          {hierarchicalData.path.join(' > ')}
        </p>
      </div>

      <p style={{
        fontSize: "1.1rem",
        color: "#666",
        marginBottom: "30px",
        textAlign: "center"
      }}>
        Please provide additional details about your product:
      </p>

      {error && (
        <div style={{
          backgroundColor: "#ffebee",
          color: "#c62828",
          padding: "12px",
          borderRadius: "4px",
          marginBottom: "20px",
          border: "1px solid #ef5350"
        }}>
          {error}
        </div>
      )}

      <form
        onSubmit={handleSubmit}
        style={{
          display: "flex",
          flexDirection: "column",
          gap: "20px",
          maxHeight: "70vh",
          overflowY: "auto",
          padding: "10px"
        }}
      >


        {/* Model Number */}
        <div>
          <label style={{ display: "block", marginBottom: "8px", fontWeight: "bold", color: "#333" }}>
            Model Number *
          </label>
          <input
            type="text"
            name="modelNumber"
            value={formData.modelNumber}
            onChange={handleInputChange}
            required
            style={{
              width: "100%",
              padding: "12px",
              border: "1px solid #ddd",
              borderRadius: "4px",
              fontSize: "16px"
            }}
            placeholder="Enter exact model number"
          />
        </div>

        {/* Serial Number */}
        <div>
          <label style={{ display: "block", marginBottom: "8px", fontWeight: "bold", color: "#333" }}>
            Serial Number *
          </label>
          <input
            type="text"
            name="serialNumber"
            value={formData.serialNumber}
            onChange={handleInputChange}
            required
            style={{
              width: "100%",
              padding: "12px",
              border: "1px solid #ddd",
              borderRadius: "4px",
              fontSize: "16px"
            }}
            placeholder="Enter serial number"
          />
        </div>

        {/* SDK Version - only show for software */}
        {hierarchicalData.productCategory === "Software" && (
          <div>
            <label style={{ display: "block", marginBottom: "8px", fontWeight: "bold", color: "#333" }}>
              SDK Version
            </label>
            <input
              type="text"
              name="sdkVersion"
              value={formData.sdkVersion}
              onChange={handleInputChange}
              style={{
                width: "100%",
                padding: "12px",
                border: "1px solid #ddd",
                borderRadius: "4px",
                fontSize: "16px"
              }}
              placeholder="e.g., 8.90, 3.1"
            />
          </div>
        )}

        {/* Software */}
        <div>
          <label style={{ display: "block", marginBottom: "8px", fontWeight: "bold", color: "#333" }}>
            Software
          </label>
          <select
            name="software"
            value={formData.software}
            onChange={handleInputChange}
            style={{
              width: "100%",
              padding: "12px",
              border: "1px solid #ddd",
              borderRadius: "4px",
              fontSize: "16px"
            }}
          >
            <option value="">Select Software</option>
            <option value="Sapera LT (v9.0)">Sapera LT (v9.0)</option>
            <option value="Spinnaker (v4.2)">Spinnaker (v4.2)</option>
          </select>
        </div>

        {/* Programming Language */}
        <div>
          <label style={{ display: "block", marginBottom: "8px", fontWeight: "bold", color: "#333" }}>
            Programming Language
          </label>
          <select
            name="programmingLanguage"
            value={formData.programmingLanguage}
            onChange={handleInputChange}
            style={{
              width: "100%",
              padding: "12px",
              border: "1px solid #ddd",
              borderRadius: "4px",
              fontSize: "16px"
            }}
          >
            <option value="">Select Language</option>
            <option value="C++">C++</option>
            <option value="C#">C#</option>
            <option value="Python">Python</option>
            <option value="Java">Java</option>
            <option value="LabVIEW">LabVIEW</option>
            <option value="Other">Other</option>
          </select>
        </div>

        {/* Operating System */}
        <div>
          <label style={{ display: "block", marginBottom: "8px", fontWeight: "bold", color: "#333" }}>
            Operating System *
          </label>
          <select
            name="operatingSystemDetailed"
            value={formData.operatingSystemDetailed}
            onChange={handleInputChange}
            required
            style={{
              width: "100%",
              padding: "12px",
              border: "1px solid #ddd",
              borderRadius: "4px",
              fontSize: "16px"
            }}
          >
            <option value="">Select Operating System</option>
            <option value="Windows 10">Windows 10</option>
            <option value="Windows 11">Windows 11</option>
            <option value="Ubuntu 18.04">Ubuntu 18.04</option>
            <option value="Ubuntu 20.04">Ubuntu 20.04</option>
            <option value="Ubuntu 22.04">Ubuntu 22.04</option>
            <option value="CentOS 7">CentOS 7</option>
            <option value="CentOS 8">CentOS 8</option>
            <option value="Other Linux">Other Linux</option>
            <option value="macOS">macOS</option>
            <option value="Other">Other</option>
          </select>
        </div>

        {/* Purchased From */}
        <div>
          <label style={{ display: "block", marginBottom: "8px", fontWeight: "bold", color: "#333" }}>
            Purchased From *
          </label>
          <input
            type="text"
            name="purchasedFrom"
            value={formData.purchasedFrom}
            onChange={handleInputChange}
            required
            style={{
              width: "100%",
              padding: "12px",
              border: "1px solid #ddd",
              borderRadius: "4px",
              fontSize: "16px"
            }}
            placeholder="Dealer/Distributor name"
          />
        </div>

        {/* Year of Purchase */}
        <div>
          <label style={{ display: "block", marginBottom: "8px", fontWeight: "bold", color: "#333" }}>
            Year of Purchase *
          </label>
          <select
            name="yearOfPurchase"
            value={formData.yearOfPurchase}
            onChange={handleInputChange}
            required
            style={{
              width: "100%",
              padding: "12px",
              border: "1px solid #ddd",
              borderRadius: "4px",
              fontSize: "16px"
            }}
          >
            <option value="">Select Year</option>
            {Array.from({ length: 10 }, (_, i) => new Date().getFullYear() - i).map(year => (
              <option key={year} value={year}>{year}</option>
            ))}
          </select>
        </div>

        {/* PO Number */}
        <div>
          <label style={{ display: "block", marginBottom: "8px", fontWeight: "bold", color: "#333" }}>
            PO Number *
          </label>
          <input
            type="text"
            name="poNumber"
            value={formData.poNumber}
            onChange={handleInputChange}
            required
            style={{
              width: "100%",
              padding: "12px",
              border: "1px solid #ddd",
              borderRadius: "4px",
              fontSize: "16px"
            }}
            placeholder="Purchase Order Number"
          />
        </div>

        {/* Submit Buttons */}
        <div style={{ 
          display: "flex", 
          gap: "15px", 
          justifyContent: "center",
          marginTop: "30px"
        }}>
          <button
            type="button"
            onClick={handleCancel}
            style={{
              padding: "15px 30px",
              backgroundColor: "#757575",
              color: "white",
              border: "none",
              borderRadius: "6px",
              fontSize: "16px",
              cursor: "pointer",
              transition: "background-color 0.2s ease"
            }}
          >
            Back
          </button>
          
          <button
            type="submit"
            disabled={loading}
            style={{
              padding: "15px 30px",
              backgroundColor: loading ? "#ccc" : "#4CAF50",
              color: "white",
              border: "none",
              borderRadius: "6px",
              fontSize: "16px",
              fontWeight: "bold",
              cursor: loading ? "not-allowed" : "pointer",
              transition: "background-color 0.2s ease"
            }}
          >
            {loading ? "Creating Ticket..." : "Continue"}
          </button>
        </div>
      </form>
    </div>
  );
}
